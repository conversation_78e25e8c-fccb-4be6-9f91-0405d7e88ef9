var { createConnection } = require('../database');
var moment = require('moment');
var utils = require('./../utils');

const _TABLE = 'reservations';
const _ID = 'id_reservation'; 

const response = {
    status: 200,
    results: null
}

exports.get = (req, res) => {
    
    if (req.query.init_date && req.query.end_date) {
        sql = `SELECT * FROM ${_TABLE} WHERE (createdAt BETWEEN '${req.query.init_date}' AND '${req.query.end_date}');`;

    }else{
        sql = `SELECT * from ${_TABLE} `;
    }

    createConnection().query(sql, (error, result) => {
        if (error) throw error;

        if(result.length > 0){
            result.forEach((item, pos) => {
                let date_aux = item.createdAt;
                var local_date= moment.utc(date_aux).local().format('YYYY-MM-DD HH:mm:ss');
                item.createdAt = local_date;
            });
        }

        response.results = result;
        res.status(200).json(response);
    });
}

exports.getById = (req, res) => {

    sql = `SELECT * from ${ _TABLE } WHERE ${ _ID } = "${ req.params.id }"`;

    createConnection().query(sql, (error, result) => {

        if (error) throw error;
        res.status(200).json(result);

    });
}
exports.getByFolio= (req, res) => {

    sql = `SELECT * from ${ _TABLE } WHERE folio = "${ req.params.id }"`;

    createConnection().query(sql, (error, result) => {

        if (error) throw error;
        res.status(200).json(result);

    });
}

exports.insert = (req, res) => {
    
    try {
        // Log incoming request for debugging
        console.log('=== RESERVATION REQUEST DEBUG ===');
        console.log('Request body:', JSON.stringify(req.body, null, 2));
        console.log('Request body type:', typeof req.body);
        console.log('Request body keys:', Object.keys(req.body));
        console.log('=================================');

        // Validate only required fields for reservations
        const validateReservation = (data) => {
            const requiredFields = [
                'folio',
                'trip_type',
                'unit',
                'pickup_location',
                'destination_location',
                'total_passengers',
                'fullname',
                'member_id',
                'email',
                'cellphone',
                'payment_method',
                'total_payment'
            ];

            console.log('=== VALIDATION DEBUG ===');
            console.log('Required fields:', requiredFields);

            for (let field of requiredFields) {
                const value = data[field];
                console.log(`Field "${field}":`, value, `(type: ${typeof value})`);

                if (value === undefined || value === null || value === '') {
                    console.log(`❌ Field "${field}" is missing or empty`);
                    return false;
                }
            }

            console.log('✅ All required fields are present');
            console.log('========================');
            return true;
        };

        const isValid = validateReservation(req.body);

        if (isValid) {
            console.log('Proceeding with database insert...');
            sql = `INSERT INTO ${ _TABLE } SET ?`;

            // Log the data being sent to help debug
            console.log('=== RESERVATION INSERT DEBUG ===');
            console.log('Table:', _TABLE);
            console.log('Data being inserted:', JSON.stringify(req.body, null, 2));
            console.log('SQL Query:', sql);
            console.log('================================');

            createConnection().query(sql, req.body, (err, result) => {
                let status, _res;
                if (err) {
                    status = 500;

                    // Enhanced error logging
                    console.error('=== DATABASE ERROR ===');
                    console.error('Error Code:', err.code);
                    console.error('Error Number:', err.errno);
                    console.error('SQL State:', err.sqlState);
                    console.error('Error Message:', err.message);
                    console.error('Full Error:', err);
                    console.error('======================');

                    // Return more detailed error information
                    _res = {
                        error: true,
                        message: "Database error occurred",
                        details: {
                            code: err.code,
                            errno: err.errno,
                            sqlState: err.sqlState,
                            message: err.message
                        }
                    };
                }else{
                    status = 200;
                    _res = result;
                    console.log('=== INSERT SUCCESS ===');
                    console.log('Insert ID:', result.insertId);
                    console.log('Affected Rows:', result.affectedRows);
                    console.log('======================');
                }

                res.status(status).json(_res);
            });

        } else {
            console.log('=== VALIDATION ERROR ===');
            console.log('Missing parameters in request body:', req.body);
            console.log('========================');
            res.status(405).json({message: "Missing Parameters"});
        }
        
    } catch (e) {
        console.error('=== CATCH BLOCK ERROR ===');
        console.error('Error in reservations insert:', e);
        console.error('Stack trace:', e.stack);
        console.error('========================');

        res.status(500).json({
            error: true,
            message: "Internal server error",
            details: e.message
        });
    }
}

exports.update = (req, res) =>{

    try {
        // Log incoming update request for debugging
        console.log('=== RESERVATION UPDATE DEBUG ===');
        console.log('Update ID:', req.params.id);
        console.log('Update data:', JSON.stringify(req.body, null, 2));
        console.log('================================');

        utils.functionValidateObj(req.body, (resp)=>{

            if(resp){

                sql = `UPDATE ${ _TABLE } SET `;
                let data = [];
                let aux = 0;

                Object.keys(req.body).forEach( (e) => {

                    sql += (aux > 0) ? ',' + e + '= ?' : e + '= ?';
                    aux++;
                    data.push(req.body[e]);

                });

                sql += ` WHERE ${ _ID } = ? `;
                data.push(req.params.id);

                createConnection().query(sql, data,(err, result) => {

                    let status, _res;
                    if (err) {
                        status = 500;
                        _res = err;
                    }else{
                        status = 200;
                        _res = result;
                    }

                    res.status(status).json(_res);
                });

            }else{
                res.status(405).json({message: "Missing Parameters"});
            }

        });

    } catch (error) {
        console.error('=== UPDATE CATCH BLOCK ERROR ===');
        console.error('Error in reservations update:', error);
        console.error('Stack trace:', error.stack);
        console.error('===============================');

        res.status(500).json({
            error: true,
            message: "Internal server error in update",
            details: error.message
        });
    }
}

exports._delete = (req, res) =>{
    try {

        sql = `UPDATE ${ _TABLE } SET ACTIVO = ? WHERE  ${ _ID } = ?`;

        let data = [
            3,
            req.params.id
        ];

        createConnection().query(sql, data, (err, result) =>{

            let status, _res;
            if (err) {
                status = 500;
                _res = err;
            }else{
                status = 200;
                _res = result;
            }
            res.status(status).json(_res);
        });

    } catch (error) {
        console.error('=== UPDATE CATCH BLOCK ERROR ===');
        console.error('Error in reservations update:', error);
        console.error('Stack trace:', error.stack);
        console.error('===============================');

        res.status(500).json({
            error: true,
            message: "Internal server error in update",
            details: error.message
        });
    }
}

