var { createConnection } = require('../database');
var moment = require('moment');
var utils = require('./../utils');

const _TABLE = 'reservations';
const _ID = 'id_reservation'; 

const response = {
    status: 200,
    results: null
}

exports.get = (req, res) => {
    
    if (req.query.init_date && req.query.end_date) {
        sql = `SELECT * FROM ${_TABLE} WHERE (createdAt BETWEEN '${req.query.init_date}' AND '${req.query.end_date}');`;

    }else{
        sql = `SELECT * from ${_TABLE} `;
    }

    createConnection().query(sql, (error, result) => {
        if (error) throw error;

        if(result.length > 0){
            result.forEach((item, pos) => {
                let date_aux = item.createdAt;
                var local_date= moment.utc(date_aux).local().format('YYYY-MM-DD HH:mm:ss');
                item.createdAt = local_date;
            });
        }

        response.results = result;
        res.status(200).json(response);
    });
}

exports.getById = (req, res) => {

    sql = `SELECT * from ${ _TABLE } WHERE ${ _ID } = "${ req.params.id }"`;

    createConnection().query(sql, (error, result) => {

        if (error) throw error;
        res.status(200).json(result);

    });
}
exports.getByFolio= (req, res) => {

    sql = `SELECT * from ${ _TABLE } WHERE folio = "${ req.params.id }"`;

    createConnection().query(sql, (error, result) => {

        if (error) throw error;
        res.status(200).json(result);

    });
}

exports.insert = (req, res) => {
    
    try {
        utils.functionValidate(req.body, (resp)=>{
            console.log(req.body)
            if(resp){
                sql = `INSERT INTO ${ _TABLE } SET ?`;

                createConnection().query(sql, req.body, (err, result) => {
                    let status, _res;
                    if (err) {
                        status = 500;
                        _res = err;
                    }else{
                        status = 200;
                        _res = result;
                    }
                    
                    res.status(status).json(_res);
                });

            }else{
                res.status(405).json({message: "Missing Parameters"});
            }

        });
        
    } catch (e) {
        response.status = 500;
        res.status(200).json(response);
    }
}

exports.update = (req, res) =>{

    try {
        utils.functionValidate(req.body, (resp)=>{

            if(resp){

                sql = `UPDATE ${ _TABLE } SET `;
                let data = [];
                let aux = 0;

                Object.keys(req.body).forEach( (e) => {

                    sql += (aux > 0) ? ',' + e + '= ?' : e + '= ?';
                    aux++;
                    data.push(req.body[e]);

                });

                sql += ` WHERE ${ _ID } = ? `;
                data.push(req.params.id);

                createConnection().query(sql, data,(err, result) => {

                    let status, _res;
                    if (err) {
                        status = 500;
                        _res = err;
                    }else{
                        status = 200;
                        _res = result;
                    }

                    res.status(status).json(_res);
                });

            }else{
                res.status(405).json({message: "Missing Parameters"});
            }

        });

    } catch (error) {
        response.status = 500;
        res.status(200).json(response);
    }
}

exports._delete = (req, res) =>{
    try {

        sql = `UPDATE ${ _TABLE } SET ACTIVO = ? WHERE  ${ _ID } = ?`;

        let data = [
            3,
            req.params.id
        ];

        createConnection().query(sql, data, (err, result) =>{

            let status, _res;
            if (err) {
                status = 500;
                _res = err;
            }else{
                status = 200;
                _res = result;
            }
            res.status(status).json(_res);
        });

    } catch (error) {
        res.status(500).json({message: status});
    }
}

